// =====================================================
// Accommodation Data Validation Utilities
// =====================================================
// Client-side validation functions for accommodation data

import type { 
  AccommodationFormData, 
  AccommodationType, 
  AccommodationStatus,
  AccommodationFormErrors 
} from '@/types/accommodation';
import { ACCOMMODATION_TYPE_LABELS, ACCOMMODATION_AMENITIES } from '@/types/accommodation';

// =====================================================
// Validation Rules
// =====================================================

/**
 * Validate accommodation name
 */
export function validateName(name: string): string | null {
  if (!name || name.trim().length === 0) {
    return 'Name is required';
  }
  
  if (name.trim().length < 3) {
    return 'Name must be at least 3 characters long';
  }
  
  if (name.trim().length > 100) {
    return 'Name must be less than 100 characters';
  }
  
  return null;
}

/**
 * Validate accommodation type
 */
export function validateType(type: string): string | null {
  if (!type) {
    return 'Type is required';
  }
  
  if (!Object.keys(ACCOMMODATION_TYPE_LABELS).includes(type)) {
    return 'Invalid accommodation type';
  }
  
  return null;
}

/**
 * Validate accommodation description
 */
export function validateDescription(description: string): string | null {
  if (!description || description.trim().length === 0) {
    return 'Description is required';
  }
  
  if (description.trim().length < 10) {
    return 'Description must be at least 10 characters long';
  }
  
  if (description.trim().length > 2000) {
    return 'Description must be less than 2000 characters';
  }
  
  return null;
}

/**
 * Validate price range
 */
export function validatePriceRange(priceRange: string): string | null {
  if (!priceRange || priceRange.trim().length === 0) {
    return 'Price range is required';
  }
  
  // Check for valid price range format (e.g., "$180–$380", "$180-$380", "$180")
  const priceRegex = /^\$\d+(?:[–-]\$?\d+)?$/;
  if (!priceRegex.test(priceRange.trim())) {
    return 'Price range must be in format "$180–$380" or "$180"';
  }
  
  return null;
}

/**
 * Validate capacity
 */
export function validateCapacity(capacity: number): string | null {
  if (capacity === null || capacity === undefined) {
    return 'Capacity is required';
  }
  
  if (!Number.isInteger(capacity) || capacity < 1) {
    return 'Capacity must be a positive integer';
  }
  
  if (capacity > 20) {
    return 'Capacity cannot exceed 20 guests';
  }
  
  return null;
}

/**
 * Validate amenities
 */
export function validateAmenities(amenities: string[]): string | null {
  if (!Array.isArray(amenities)) {
    return 'Amenities must be an array';
  }
  
  // Check if all amenities are valid
  const validAmenities = ACCOMMODATION_AMENITIES;
  const invalidAmenities = amenities.filter(amenity => !validAmenities.includes(amenity as any));
  
  if (invalidAmenities.length > 0) {
    return `Invalid amenities: ${invalidAmenities.join(', ')}`;
  }
  
  return null;
}

/**
 * Validate special features
 */
export function validateSpecialFeatures(specialFeatures: string): string | null {
  if (specialFeatures && specialFeatures.length > 500) {
    return 'Special features must be less than 500 characters';
  }
  
  return null;
}

/**
 * Validate images
 */
export function validateImages(images: File[], existingImages: string[]): string | null {
  const totalImages = images.length + existingImages.length;
  
  if (totalImages === 0) {
    return 'At least one image is required';
  }
  
  if (totalImages > 10) {
    return 'Maximum 10 images allowed';
  }
  
  // Validate file types and sizes
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  for (const file of images) {
    if (!allowedTypes.includes(file.type)) {
      return `Invalid file type: ${file.name}. Only JPEG, PNG, and WebP are allowed.`;
    }
    
    if (file.size > maxSize) {
      return `File too large: ${file.name}. Maximum size is 10MB.`;
    }
  }
  
  return null;
}

/**
 * Validate status
 */
export function validateStatus(status: string): string | null {
  if (!status) {
    return 'Status is required';
  }
  
  if (!['draft', 'published', 'unpublished'].includes(status)) {
    return 'Invalid status';
  }
  
  return null;
}

// =====================================================
// Comprehensive Validation
// =====================================================

/**
 * Validate complete accommodation form data
 */
export function validateAccommodationForm(data: AccommodationFormData): AccommodationFormErrors {
  const errors: AccommodationFormErrors = {};
  
  // Validate each field
  const nameError = validateName(data.name);
  if (nameError) errors.name = nameError;
  
  const typeError = validateType(data.type);
  if (typeError) errors.type = typeError;
  
  const descriptionError = validateDescription(data.description);
  if (descriptionError) errors.description = descriptionError;
  
  const priceRangeError = validatePriceRange(data.price_range);
  if (priceRangeError) errors.price_range = priceRangeError;
  
  const capacityError = validateCapacity(data.capacity);
  if (capacityError) errors.capacity = capacityError;
  
  const amenitiesError = validateAmenities(data.amenities);
  if (amenitiesError) errors.amenities = amenitiesError;
  
  const specialFeaturesError = validateSpecialFeatures(data.special_features);
  if (specialFeaturesError) errors.general = specialFeaturesError;
  
  const imagesError = validateImages(data.images, data.existing_images);
  if (imagesError) errors.images = imagesError;
  
  const statusError = validateStatus(data.status);
  if (statusError) errors.general = statusError;
  
  return errors;
}

/**
 * Check if form has any validation errors
 */
export function hasValidationErrors(errors: AccommodationFormErrors): boolean {
  return Object.keys(errors).length > 0;
}

/**
 * Get first validation error message
 */
export function getFirstValidationError(errors: AccommodationFormErrors): string | null {
  const errorKeys = Object.keys(errors);
  if (errorKeys.length === 0) return null;
  
  const firstKey = errorKeys[0] as keyof AccommodationFormErrors;
  return errors[firstKey] || null;
}

// =====================================================
// Data Sanitization
// =====================================================

/**
 * Sanitize accommodation form data
 */
export function sanitizeAccommodationData(data: AccommodationFormData): AccommodationFormData {
  return {
    name: data.name.trim(),
    type: data.type,
    description: data.description.trim(),
    special_features: data.special_features.trim(),
    amenities: data.amenities.filter(amenity => amenity.trim().length > 0),
    price_range: data.price_range.trim(),
    capacity: Math.max(1, Math.floor(data.capacity)),
    images: data.images,
    existing_images: data.existing_images,
    status: data.status,
    featured: Boolean(data.featured)
  };
}

/**
 * Prepare data for API submission
 */
export function prepareAccommodationForSubmission(data: AccommodationFormData) {
  const sanitized = sanitizeAccommodationData(data);
  
  return {
    name: sanitized.name,
    type: sanitized.type,
    description: sanitized.description,
    special_features: sanitized.special_features || undefined,
    amenities: sanitized.amenities,
    price_range: sanitized.price_range,
    capacity: sanitized.capacity,
    status: sanitized.status,
    featured: sanitized.featured
  };
}

// =====================================================
// Real-time Validation Helpers
// =====================================================

/**
 * Debounced validation for real-time feedback
 */
export function createDebouncedValidator<T>(
  validator: (value: T) => string | null,
  delay: number = 300
) {
  let timeoutId: NodeJS.Timeout;
  
  return (value: T, callback: (error: string | null) => void) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => {
      const error = validator(value);
      callback(error);
    }, delay);
  };
}

/**
 * Validate field on blur
 */
export function validateFieldOnBlur(
  fieldName: keyof AccommodationFormData,
  value: any,
  setError: (field: string, error: string | null) => void
) {
  let error: string | null = null;
  
  switch (fieldName) {
    case 'name':
      error = validateName(value);
      break;
    case 'type':
      error = validateType(value);
      break;
    case 'description':
      error = validateDescription(value);
      break;
    case 'price_range':
      error = validatePriceRange(value);
      break;
    case 'capacity':
      error = validateCapacity(value);
      break;
    case 'amenities':
      error = validateAmenities(value);
      break;
    case 'special_features':
      error = validateSpecialFeatures(value);
      break;
    case 'status':
      error = validateStatus(value);
      break;
  }
  
  setError(fieldName, error);
}
