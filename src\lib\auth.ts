import { supabase } from "./supabase";
import type { User } from "@supabase/supabase-js";

export interface UserProfile {
  id: string;
  email: string;
  role: "admin" | "staff";
}

export interface AuthState {
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
}

// Sign in with email and password
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) throw error;
  return data;
};

// Sign out
export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
};

// Get current user
export const getCurrentUser = async () => {
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();
  if (error) throw error;
  return user;
};

// Get user profile with role
export const getUserProfile = async (
  userId: string
): Promise<UserProfile | null> => {
  try {
    if (process.env.NODE_ENV === "development") {
      console.log("Fetching profile for user ID:", userId);
    }

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching user profile:", error);
      if (process.env.NODE_ENV === "development") {
        console.error("Profile fetch error details:", {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint,
        });
      }
      return null;
    }

    if (process.env.NODE_ENV === "development") {
      console.log("Profile data received:", data);
    }

    return data;
  } catch (err) {
    console.error("Unexpected error in getUserProfile:", err);
    return null;
  }
};

// Check if user has admin role
export const isAdmin = (profile: UserProfile | null): boolean => {
  return profile?.role === "admin";
};

// Check if user has staff role or higher
export const isStaffOrAdmin = (profile: UserProfile | null): boolean => {
  return profile?.role === "staff" || profile?.role === "admin";
};

// Create profile for a new user (call this after user registration)
export const createUserProfile = async (
  userId: string,
  email: string,
  metadata: Record<string, any> = {}
) => {
  const { error } = await supabase.rpc("create_profile_for_user", {
    user_id: userId,
    user_email: email,
    user_metadata: metadata,
  });

  if (error) {
    console.error("Error creating user profile:", error);
    throw error;
  }
};

// Update last login timestamp
export const updateLastLogin = async (userId: string) => {
  const { error } = await supabase.rpc("update_user_last_login", {
    user_id: userId,
  });

  if (error) {
    console.warn("Error updating last login:", error);
  }
};
