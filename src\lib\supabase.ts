import { createClient } from "@supabase/supabase-js";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error("Missing Supabase environment variables");
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database types
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          role: "admin" | "staff";
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          role?: "admin" | "staff";
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          role?: "admin" | "staff";
          created_at?: string;
          updated_at?: string;
        };
      };
      accommodations: {
        Row: {
          id: string;
          name: string;
          type: string;
          description: string;
          special_features?: string;
          amenities: string[];
          price_range: string;
          capacity: number;
          images: string[];
          status: "draft" | "published" | "unpublished";
          featured: boolean;
          created_at: string;
          updated_at: string;
          created_by?: string;
          updated_by?: string;
        };
        Insert: {
          id?: string;
          name: string;
          type: string;
          description: string;
          special_features?: string;
          amenities?: string[];
          price_range: string;
          capacity: number;
          images?: string[];
          status?: "draft" | "published" | "unpublished";
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
          created_by?: string;
          updated_by?: string;
        };
        Update: {
          id?: string;
          name?: string;
          type?: string;
          description?: string;
          special_features?: string;
          amenities?: string[];
          price_range?: string;
          capacity?: number;
          images?: string[];
          status?: "draft" | "published" | "unpublished";
          featured?: boolean;
          created_at?: string;
          updated_at?: string;
          created_by?: string;
          updated_by?: string;
        };
      };
    };
  };
}
