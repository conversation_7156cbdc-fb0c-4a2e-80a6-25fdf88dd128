import { supabase } from './supabase';
import { toast } from 'sonner';

/**
 * Global error handler for API requests
 * Handles authentication errors and other common API errors
 */
export class ApiErrorHandler {
  private static instance: ApiErrorHandler;
  private signOutCallback: (() => Promise<void>) | null = null;

  private constructor() {}

  static getInstance(): ApiErrorHandler {
    if (!ApiErrorHandler.instance) {
      ApiErrorHandler.instance = new ApiErrorHandler();
    }
    return ApiErrorHandler.instance;
  }

  /**
   * Set the sign out callback to be called when authentication errors occur
   */
  setSignOutCallback(callback: () => Promise<void>) {
    this.signOutCallback = callback;
  }

  /**
   * Handle API errors globally
   */
  async handleError(error: any, context?: string): Promise<boolean> {
    const errorMessage = error?.message || 'An unexpected error occurred';
    const errorCode = error?.code;

    // Log error for debugging
    console.error(`API Error${context ? ` in ${context}` : ''}:`, {
      message: errorMessage,
      code: errorCode,
      error
    });

    // Check if this is an authentication error
    if (this.isAuthError(error)) {
      console.log('Authentication error detected, signing out user');
      
      // Show user-friendly message
      toast.error('Your session has expired. Please sign in again.');
      
      // Sign out the user
      if (this.signOutCallback) {
        try {
          await this.signOutCallback();
        } catch (signOutError) {
          console.error('Error during forced sign out:', signOutError);
        }
      } else {
        // Fallback: direct sign out
        await supabase.auth.signOut();
      }
      
      return true; // Indicates error was handled
    }

    // Handle other common errors
    if (errorCode === 'PGRST116') {
      toast.error('The requested resource was not found.');
      return true;
    }

    if (errorCode === 'PGRST204') {
      toast.error('You do not have permission to perform this action.');
      return true;
    }

    if (errorMessage.includes('Network')) {
      toast.error('Network error. Please check your connection and try again.');
      return true;
    }

    // For unhandled errors, show a generic message
    if (context) {
      toast.error(`Error in ${context}: ${errorMessage}`);
    } else {
      toast.error(errorMessage);
    }

    return false; // Indicates error was not specifically handled
  }

  /**
   * Check if an error is authentication-related
   */
  private isAuthError(error: any): boolean {
    const message = error?.message?.toLowerCase() || '';
    const code = error?.code;

    return (
      code === 'invalid_token' ||
      code === 'PGRST301' ||
      message.includes('jwt') ||
      message.includes('expired') ||
      message.includes('invalid') ||
      message.includes('unauthorized') ||
      message.includes('authentication') ||
      message.includes('session')
    );
  }

  /**
   * Wrapper for API calls that automatically handles errors
   */
  async withErrorHandling<T>(
    apiCall: () => Promise<T>,
    context?: string
  ): Promise<T | null> {
    try {
      return await apiCall();
    } catch (error) {
      await this.handleError(error, context);
      return null;
    }
  }
}

// Export singleton instance
export const apiErrorHandler = ApiErrorHandler.getInstance();

/**
 * Decorator for API functions to automatically handle errors
 */
export function withApiErrorHandling(context?: string) {
  return function <T extends (...args: any[]) => Promise<any>>(
    target: any,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      try {
        return await originalMethod.apply(this, args);
      } catch (error) {
        await apiErrorHandler.handleError(error, context || propertyKey);
        throw error; // Re-throw so calling code can handle it if needed
      }
    };

    return descriptor;
  };
}
