import { useEffect, useRef } from "react";
import { useAuth } from "./use-auth";
import { isSessionValid } from "@/lib/auth";

interface SessionMonitorOptions {
  checkInterval?: number; // in milliseconds
  enabled?: boolean;
}

/**
 * Hook to monitor session validity and automatically sign out users when sessions expire
 */
export const useSessionMonitor = (options: SessionMonitorOptions = {}) => {
  const {
    checkInterval = 60000, // Check every minute by default
    enabled = true,
  } = options;

  const { user, signOut } = useAuth();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastCheckRef = useRef<number>(0);

  useEffect(() => {
    if (!enabled || !user) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    const checkSession = async () => {
      try {
        const now = Date.now();

        // Avoid checking too frequently
        if (now - lastCheckRef.current < checkInterval) {
          return;
        }

        lastCheckRef.current = now;

        const valid = await isSessionValid();

        if (!valid) {
          console.log("Session expired during monitoring, signing out user");
          if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
          }
          await signOut();
        }
      } catch (error) {
        console.error("Error during session check:", error);
        // On error, assume session is invalid and sign out
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }
        await signOut();
      }
    };

    // Handle page visibility changes - check session when user returns to tab
    const handleVisibilityChange = () => {
      if (!document.hidden && user) {
        console.log("Page became visible, checking session validity");
        checkSession();
      }
    };

    // Start monitoring
    intervalRef.current = setInterval(checkSession, checkInterval);

    // Listen for page visibility changes
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Also check immediately
    checkSession();

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [user, enabled, checkInterval, signOut]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);
};
