import React from "react";
import { AuthContext, useAuthState } from "@/hooks/use-auth";
import { useSessionMonitor } from "@/hooks/use-session-monitor";

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const authState = useAuthState();

  // Monitor session validity and auto-logout on expiration
  useSessionMonitor({
    checkInterval: 60000, // Check every minute
    enabled: !!authState.user && !authState.loading,
  });

  return (
    <AuthContext.Provider value={authState}>{children}</AuthContext.Provider>
  );
};
