import { useState, useEffect, createContext, useContext } from "react";
import { supabase } from "@/lib/supabase";
import {
  getCurrentUser,
  getUserProfile,
  type AuthState,
  type UserProfile,
} from "@/lib/auth";
import type { User, AuthError } from "@supabase/supabase-js";

const AuthContext = createContext<
  AuthState & {
    signIn: (email: string, password: string) => Promise<void>;
    signOut: () => Promise<void>;
    refreshProfile: () => Promise<void>;
  }
>({
  user: null,
  profile: null,
  loading: true,
  signIn: async () => {},
  signOut: async () => {},
  refreshProfile: async () => {},
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const useAuthState = () => {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    loading: true,
  });

  const refreshProfile = async (user: User | null) => {
    if (process.env.NODE_ENV === "development") {
      console.log("refreshProfile called with user:", user?.id);
    }

    if (!user) {
      setState((prev) => ({ ...prev, profile: null, loading: false }));
      return;
    }

    try {
      const profile = await getUserProfile(user.id);

      if (process.env.NODE_ENV === "development") {
        console.log("Profile fetched successfully:", profile);
      }

      // Always set loading to false, whether profile is found or not
      setState((prev) => ({ ...prev, profile, loading: false }));
    } catch (error) {
      console.error("Error fetching profile:", error);

      // Check if this is an authentication error
      const authError = error as AuthError;
      if (
        authError?.message?.includes("JWT") ||
        authError?.message?.includes("expired") ||
        authError?.message?.includes("invalid") ||
        authError?.code === "invalid_token" ||
        authError?.code === "PGRST301"
      ) {
        // Session is invalid, sign out the user
        console.log("Authentication error, signing out user");
        try {
          await supabase.auth.signOut();
        } catch (signOutError) {
          console.error("Error during sign out:", signOutError);
        }
        setState((prev) => ({
          ...prev,
          user: null,
          profile: null,
          loading: false,
        }));
        return;
      }

      // For other errors, ALWAYS set loading to false
      console.log("Setting loading to false due to profile fetch error");
      setState((prev) => ({ ...prev, profile: null, loading: false }));
    }
  };

  const signIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;

    // Update last login timestamp
    if (data.user) {
      try {
        await supabase.rpc("update_user_last_login", { user_id: data.user.id });
      } catch (loginError) {
        console.warn("Failed to update last login:", loginError);
      }
    }

    // Profile will be updated by the auth state change listener
  };

  const signOut = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  const refreshProfileManual = async () => {
    if (state.user) {
      await refreshProfile(state.user);
    }
  };

  useEffect(() => {
    // Safety timeout to prevent infinite loading
    const safetyTimeout = setTimeout(() => {
      setState((prev) => {
        if (prev.loading) {
          console.warn(
            "Auth loading timeout reached, forcing loading to false"
          );
          return { ...prev, loading: false };
        }
        return prev;
      });
    }, 10000); // 10 second safety timeout

    // Get initial session
    const getInitialSession = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          console.error("Error getting session:", error);
          setState((prev) => ({
            ...prev,
            user: null,
            profile: null,
            loading: false,
          }));
          return;
        }

        const user = session?.user || null;
        setState((prev) => ({ ...prev, user }));
        await refreshProfile(user);
      } catch (error) {
        console.error("Error getting initial session:", error);
        setState((prev) => ({
          ...prev,
          user: null,
          profile: null,
          loading: false,
        }));
      }
    };

    getInitialSession();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (process.env.NODE_ENV === "development") {
        console.log("Auth state change:", event, session?.user?.id);
      }

      const user = session?.user || null;

      // Handle sign out
      if (event === "SIGNED_OUT") {
        setState((prev) => ({
          ...prev,
          user: null,
          profile: null,
          loading: false,
        }));
        return;
      }

      // For all other events, update user and refresh profile
      setState((prev) => ({ ...prev, user }));
      await refreshProfile(user);
    });

    return () => {
      subscription.unsubscribe();
      clearTimeout(safetyTimeout);
    };
  }, []);

  return {
    ...state,
    signIn,
    signOut,
    refreshProfile: refreshProfileManual,
  };
};

export { AuthContext };
