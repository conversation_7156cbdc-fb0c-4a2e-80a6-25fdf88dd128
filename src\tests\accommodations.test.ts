// =====================================================
// Accommodations Integration Tests
// =====================================================
// Comprehensive tests for accommodation CRUD operations,
// image handling, and data validation

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import {
  createAccommodation,
  updateAccommodation,
  deleteAccommodation,
  getAccommodation,
  getAccommodations,
  uploadAccommodationImage,
  deleteAccommodationImage,
  toggleAccommodationFeatured,
  updateAccommodationStatus
} from '@/lib/accommodations';
import {
  getPublishedAccommodations,
  getFeaturedAccommodations,
  getAccommodationsByType,
  searchPublishedAccommodations
} from '@/lib/public-accommodations';
import type { AccommodationCreateRequest } from '@/types/accommodation';

// Test data
const validAccommodationData: AccommodationCreateRequest = {
  name: 'Test Safari Suite',
  type: 'suite',
  description: 'A luxurious test suite for automated testing purposes',
  special_features: 'Automated testing features',
  amenities: ['Private bathroom', 'Air conditioning', 'Minibar'],
  price_range: '$300-$500',
  capacity: 2,
  status: 'draft',
  featured: false
};

const invalidAccommodationData = {
  name: '', // Invalid: empty name
  type: 'invalid_type', // Invalid: not in enum
  description: '', // Invalid: empty description
  price_range: '', // Invalid: empty price range
  capacity: 0, // Invalid: zero capacity
  status: 'invalid_status', // Invalid: not in enum
  featured: false
};

// Test utilities
let testAccommodationId: string | null = null;
let testImageUrl: string | null = null;

describe('Accommodations CRUD Operations', () => {
  afterEach(async () => {
    // Cleanup: delete test accommodation if it exists
    if (testAccommodationId) {
      try {
        await deleteAccommodation(testAccommodationId);
      } catch (error) {
        console.warn('Failed to cleanup test accommodation:', error);
      }
      testAccommodationId = null;
    }

    // Cleanup: delete test image if it exists
    if (testImageUrl) {
      try {
        await deleteAccommodationImage(testImageUrl);
      } catch (error) {
        console.warn('Failed to cleanup test image:', error);
      }
      testImageUrl = null;
    }
  });

  describe('Create Accommodation', () => {
    it('should create accommodation with valid data', async () => {
      const result = await createAccommodation(validAccommodationData);
      
      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data.name).toBe(validAccommodationData.name);
      expect(result.data.type).toBe(validAccommodationData.type);
      expect(result.data.status).toBe(validAccommodationData.status);
      
      testAccommodationId = result.data.id;
    });

    it('should reject accommodation with invalid data', async () => {
      const result = await createAccommodation(invalidAccommodationData as any);
      
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
    });

    it('should set default values correctly', async () => {
      const minimalData = {
        name: 'Minimal Test Suite',
        type: 'suite' as const,
        description: 'Minimal test description',
        price_range: '$200-$300',
        capacity: 1,
        status: 'draft' as const,
        featured: false
      };

      const result = await createAccommodation(minimalData);
      
      expect(result.success).toBe(true);
      expect(result.data.amenities).toEqual([]);
      expect(result.data.images).toEqual([]);
      expect(result.data.created_at).toBeDefined();
      expect(result.data.updated_at).toBeDefined();
      
      testAccommodationId = result.data.id;
    });
  });

  describe('Read Accommodation', () => {
    beforeEach(async () => {
      // Create test accommodation
      const result = await createAccommodation(validAccommodationData);
      testAccommodationId = result.data.id;
    });

    it('should retrieve accommodation by ID', async () => {
      const result = await getAccommodation(testAccommodationId!);
      
      expect(result.success).toBe(true);
      expect(result.data.id).toBe(testAccommodationId);
      expect(result.data.name).toBe(validAccommodationData.name);
    });

    it('should return error for non-existent ID', async () => {
      const result = await getAccommodation('non-existent-id');
      
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
    });

    it('should retrieve accommodations with filters', async () => {
      const filters = {
        search: 'Test',
        type: 'suite' as const,
        status: 'draft' as const,
        featured: false as const
      };

      const sort = {
        field: 'name' as const,
        direction: 'asc' as const
      };

      const pagination = { page: 1, limit: 10 };

      const result = await getAccommodations(filters, sort, pagination);
      
      expect(result.data).toBeDefined();
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.pagination).toBeDefined();
      expect(result.pagination.page).toBe(1);
      expect(result.pagination.limit).toBe(10);
    });
  });

  describe('Update Accommodation', () => {
    beforeEach(async () => {
      // Create test accommodation
      const result = await createAccommodation(validAccommodationData);
      testAccommodationId = result.data.id;
    });

    it('should update accommodation with valid data', async () => {
      const updateData = {
        id: testAccommodationId!,
        name: 'Updated Test Suite',
        description: 'Updated description',
        capacity: 4
      };

      const result = await updateAccommodation(updateData);
      
      expect(result.success).toBe(true);
      expect(result.data.name).toBe(updateData.name);
      expect(result.data.description).toBe(updateData.description);
      expect(result.data.capacity).toBe(updateData.capacity);
      expect(result.data.updated_at).not.toBe(result.data.created_at);
    });

    it('should reject update with invalid data', async () => {
      const updateData = {
        id: testAccommodationId!,
        capacity: -1, // Invalid capacity
        type: 'invalid_type' // Invalid type
      };

      const result = await updateAccommodation(updateData as any);
      
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
    });
  });

  describe('Delete Accommodation', () => {
    beforeEach(async () => {
      // Create test accommodation
      const result = await createAccommodation(validAccommodationData);
      testAccommodationId = result.data.id;
    });

    it('should delete accommodation successfully', async () => {
      const result = await deleteAccommodation(testAccommodationId!);
      
      expect(result.success).toBe(true);
      expect(result.message).toBeDefined();

      // Verify accommodation is deleted
      const getResult = await getAccommodation(testAccommodationId!);
      expect(getResult.success).toBe(false);
      
      testAccommodationId = null; // Prevent cleanup attempt
    });

    it('should handle deletion of non-existent accommodation', async () => {
      const result = await deleteAccommodation('non-existent-id');
      
      expect(result.success).toBe(false);
      expect(result.message).toBeDefined();
    });
  });

  describe('Status Management', () => {
    beforeEach(async () => {
      // Create test accommodation
      const result = await createAccommodation(validAccommodationData);
      testAccommodationId = result.data.id;
    });

    it('should update accommodation status', async () => {
      const result = await updateAccommodationStatus(testAccommodationId!, 'published');
      
      expect(result.success).toBe(true);
      expect(result.data.status).toBe('published');
    });

    it('should toggle featured status', async () => {
      // Toggle to featured
      let result = await toggleAccommodationFeatured(testAccommodationId!, true);
      expect(result.success).toBe(true);
      expect(result.data.featured).toBe(true);

      // Toggle back to not featured
      result = await toggleAccommodationFeatured(testAccommodationId!, false);
      expect(result.success).toBe(true);
      expect(result.data.featured).toBe(false);
    });
  });
});

describe('Image Management', () => {
  beforeEach(async () => {
    // Create test accommodation
    const result = await createAccommodation(validAccommodationData);
    testAccommodationId = result.data.id;
  });

  afterEach(async () => {
    // Cleanup
    if (testAccommodationId) {
      await deleteAccommodation(testAccommodationId);
      testAccommodationId = null;
    }
    if (testImageUrl) {
      await deleteAccommodationImage(testImageUrl);
      testImageUrl = null;
    }
  });

  it('should upload image successfully', async () => {
    // Create a test file
    const testFile = new File(['test image content'], 'test-image.jpg', {
      type: 'image/jpeg'
    });

    const result = await uploadAccommodationImage(testFile, testAccommodationId!);
    
    expect(result.success).toBe(true);
    expect(result.url).toBeDefined();
    expect(result.path).toBeDefined();
    
    testImageUrl = result.url;
  });

  it('should delete image successfully', async () => {
    // First upload an image
    const testFile = new File(['test image content'], 'test-image.jpg', {
      type: 'image/jpeg'
    });

    const uploadResult = await uploadAccommodationImage(testFile, testAccommodationId!);
    expect(uploadResult.success).toBe(true);

    // Then delete it
    const deleteResult = await deleteAccommodationImage(uploadResult.url);
    expect(deleteResult.success).toBe(true);
  });
});

describe('Public API Integration', () => {
  beforeEach(async () => {
    // Create published test accommodation
    const publishedData = {
      ...validAccommodationData,
      status: 'published' as const,
      featured: true
    };
    
    const result = await createAccommodation(publishedData);
    testAccommodationId = result.data.id;
  });

  afterEach(async () => {
    if (testAccommodationId) {
      await deleteAccommodation(testAccommodationId);
      testAccommodationId = null;
    }
  });

  it('should retrieve published accommodations', async () => {
    const accommodations = await getPublishedAccommodations();
    
    expect(Array.isArray(accommodations)).toBe(true);
    // Should include our test accommodation
    const testAccommodation = accommodations.find(acc => acc.id === testAccommodationId);
    expect(testAccommodation).toBeDefined();
    expect(testAccommodation?.status).toBe('published');
  });

  it('should retrieve featured accommodations', async () => {
    const accommodations = await getFeaturedAccommodations(10);
    
    expect(Array.isArray(accommodations)).toBe(true);
    // Should include our featured test accommodation
    const testAccommodation = accommodations.find(acc => acc.id === testAccommodationId);
    expect(testAccommodation).toBeDefined();
    expect(testAccommodation?.featured).toBe(true);
  });

  it('should retrieve accommodations by type', async () => {
    const accommodations = await getAccommodationsByType('suite');
    
    expect(Array.isArray(accommodations)).toBe(true);
    // Should include our test suite
    const testAccommodation = accommodations.find(acc => acc.id === testAccommodationId);
    expect(testAccommodation).toBeDefined();
    expect(testAccommodation?.type).toBe('suite');
  });

  it('should search accommodations', async () => {
    const accommodations = await searchPublishedAccommodations('Test Safari');
    
    expect(Array.isArray(accommodations)).toBe(true);
    // Should include our test accommodation
    const testAccommodation = accommodations.find(acc => acc.id === testAccommodationId);
    expect(testAccommodation).toBeDefined();
  });
});

describe('Data Validation', () => {
  it('should validate required fields', async () => {
    const invalidData = {
      name: '',
      type: 'suite',
      description: '',
      price_range: '',
      capacity: 0,
      status: 'draft',
      featured: false
    };

    const result = await createAccommodation(invalidData as any);
    expect(result.success).toBe(false);
  });

  it('should validate enum values', async () => {
    const invalidData = {
      ...validAccommodationData,
      type: 'invalid_type',
      status: 'invalid_status'
    };

    const result = await createAccommodation(invalidData as any);
    expect(result.success).toBe(false);
  });

  it('should validate capacity constraints', async () => {
    const invalidData = {
      ...validAccommodationData,
      capacity: -1
    };

    const result = await createAccommodation(invalidData);
    expect(result.success).toBe(false);
  });
});
