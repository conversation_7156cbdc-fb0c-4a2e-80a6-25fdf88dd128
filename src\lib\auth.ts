import { supabase } from "./supabase";
import type { User } from "@supabase/supabase-js";

export interface UserProfile {
  id: string;
  email: string;
  role: "admin" | "staff";
}

export interface AuthState {
  user: User | null;
  profile: UserProfile | null;
  loading: boolean;
}

// Sign in with email and password
export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) throw error;
  return data;
};

// Sign out
export const signOut = async () => {
  const { error } = await supabase.auth.signOut();
  if (error) throw error;
};

// Get current user
export const getCurrentUser = async () => {
  const {
    data: { user },
    error,
  } = await supabase.auth.getUser();
  if (error) throw error;
  return user;
};

// Get user profile with role
export const getUserProfile = async (
  userId: string
): Promise<UserProfile | null> => {
  try {
    if (process.env.NODE_ENV === "development") {
      console.log("Fetching profile for user ID:", userId);
    }

    // First check if we have a valid session
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();

    if (sessionError) {
      console.error("Session error when fetching profile:", sessionError);
      throw sessionError;
    }

    if (!session) {
      console.log("No active session when fetching profile");
      return null;
    }

    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();

    if (error) {
      console.error("Error fetching user profile:", error);
      if (process.env.NODE_ENV === "development") {
        console.error("Profile fetch error details:", {
          code: error.code,
          message: error.message,
          details: error.details,
          hint: error.hint,
        });
      }

      // Check if this is an authentication-related error
      if (
        error.code === "PGRST301" ||
        error.message?.includes("JWT") ||
        error.message?.includes("expired") ||
        error.message?.includes("invalid")
      ) {
        throw error; // Re-throw auth errors so they can be handled upstream
      }

      return null;
    }

    if (process.env.NODE_ENV === "development") {
      console.log("Profile data received:", data);
    }

    return data;
  } catch (err) {
    console.error("Unexpected error in getUserProfile:", err);
    throw err; // Re-throw so the calling code can handle it appropriately
  }
};

// Check if user has admin role
export const isAdmin = (profile: UserProfile | null): boolean => {
  return profile?.role === "admin";
};

// Check if user has staff role or higher
export const isStaffOrAdmin = (profile: UserProfile | null): boolean => {
  return profile?.role === "staff" || profile?.role === "admin";
};

// Create profile for a new user (call this after user registration)
export const createUserProfile = async (
  userId: string,
  email: string,
  metadata: Record<string, any> = {}
) => {
  const { error } = await supabase.rpc("create_profile_for_user", {
    user_id: userId,
    user_email: email,
    user_metadata: metadata,
  });

  if (error) {
    console.error("Error creating user profile:", error);
    throw error;
  }
};

// Update last login timestamp
export const updateLastLogin = async (userId: string) => {
  const { error } = await supabase.rpc("update_user_last_login", {
    user_id: userId,
  });

  if (error) {
    console.warn("Error updating last login:", error);
  }
};

// Check if current session is valid
export const isSessionValid = async (): Promise<boolean> => {
  try {
    const {
      data: { session },
      error,
    } = await supabase.auth.getSession();

    if (error) {
      console.error("Error checking session validity:", error);
      return false;
    }

    if (!session) {
      return false;
    }

    // Check if token is expired
    const now = Math.floor(Date.now() / 1000);
    if (session.expires_at && session.expires_at <= now) {
      console.log("Session token has expired");
      return false;
    }

    return true;
  } catch (error) {
    console.error("Unexpected error checking session validity:", error);
    return false;
  }
};

// Force refresh the current session
export const refreshSession = async () => {
  try {
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error("Error refreshing session:", error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error("Failed to refresh session:", error);
    throw error;
  }
};
