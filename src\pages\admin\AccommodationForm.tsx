import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useForm, Controller } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Save,
  ArrowLeft,
  Upload,
  X,
  Check,
  ChevronsUpDown,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";

import {
  createAccommodation,
  updateAccommodation,
  getAccommodation,
  uploadAccommodationImage,
  deleteAccommodationImage,
} from "@/lib/accommodations";
import type {
  AccommodationFormData,
  AccommodationType,
  AccommodationStatus,
} from "@/types/accommodation";
import {
  ACCOMMODATION_TYPE_LABELS,
  ACCOMMODATION_AMENITIES,
  DEFAULT_ACCOMMODATION_FORM,
} from "@/types/accommodation";

interface AccommodationFormProps {
  mode: "create" | "edit";
}

export default function AccommodationForm({ mode }: AccommodationFormProps) {
  const navigate = useNavigate();
  const { id } = useParams();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [amenitiesOpen, setAmenitiesOpen] = useState(false);
  const [imageUploading, setImageUploading] = useState(false);

  const {
    register,
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
    reset,
  } = useForm<AccommodationFormData>({
    defaultValues: DEFAULT_ACCOMMODATION_FORM,
  });

  const watchedAmenities = watch("amenities");
  const watchedImages = watch("existing_images");

  // Load accommodation data for edit mode
  useEffect(() => {
    if (mode === "edit" && id) {
      loadAccommodation();
    }
  }, [mode, id]);

  const loadAccommodation = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const response = await getAccommodation(id);

      if (response.success) {
        const accommodation = response.data;
        reset({
          name: accommodation.name,
          type: accommodation.type,
          description: accommodation.description,
          special_features: accommodation.special_features || "",
          amenities: accommodation.amenities,
          price_range: accommodation.price_range,
          capacity: accommodation.capacity,
          images: [],
          existing_images: accommodation.images,
          status: accommodation.status,
          featured: accommodation.featured,
        });
      } else {
        toast.error("Failed to load accommodation");
        navigate("/admin/accommodations");
      }
    } catch (error) {
      console.error("Error loading accommodation:", error);
      toast.error("Failed to load accommodation");
      navigate("/admin/accommodations");
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const onSubmit = async (data: AccommodationFormData) => {
    try {
      setSaving(true);

      // Upload new images first
      const uploadedImageUrls: string[] = [];
      if (data.images.length > 0) {
        setImageUploading(true);
        for (const file of data.images) {
          const uploadResult = await uploadAccommodationImage(
            file,
            id || "temp"
          );
          if (uploadResult.success) {
            uploadedImageUrls.push(uploadResult.url);
          } else {
            toast.error(`Failed to upload image: ${file.name}`);
          }
        }
        setImageUploading(false);
      }

      // Combine existing and new images
      const allImages = [...data.existing_images, ...uploadedImageUrls];

      const accommodationData = {
        name: data.name,
        type: data.type,
        description: data.description,
        special_features: data.special_features || undefined,
        amenities: data.amenities,
        price_range: data.price_range,
        capacity: data.capacity,
        images: allImages,
        status: data.status,
        featured: data.featured,
      };

      let result;
      if (mode === "create") {
        result = await createAccommodation(accommodationData);
      } else {
        result = await updateAccommodation({ id: id!, ...accommodationData });
      }

      if (result.success) {
        toast.success(
          `Accommodation ${
            mode === "create" ? "created" : "updated"
          } successfully`
        );
        navigate("/admin/accommodations");
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      console.error("Error saving accommodation:", error);
      toast.error("Failed to save accommodation");
    } finally {
      setSaving(false);
    }
  };

  // Handle amenity selection
  const handleAmenityToggle = (amenity: string) => {
    const currentAmenities = watchedAmenities || [];
    const newAmenities = currentAmenities.includes(amenity)
      ? currentAmenities.filter((a) => a !== amenity)
      : [...currentAmenities, amenity];

    setValue("amenities", newAmenities);
  };

  // Handle image upload
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const currentImages = watch("images") || [];
    setValue("images", [...currentImages, ...files]);
  };

  // Handle image removal
  const handleImageRemove = (index: number, isExisting: boolean) => {
    if (isExisting) {
      const currentImages = watchedImages || [];
      const newImages = currentImages.filter((_, i) => i !== index);
      setValue("existing_images", newImages);
    } else {
      const currentImages = watch("images") || [];
      const newImages = currentImages.filter((_, i) => i !== index);
      setValue("images", newImages);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          onClick={() => navigate("/admin/accommodations")}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            {mode === "create" ? "Add New Accommodation" : "Edit Accommodation"}
          </h1>
          <p className="text-gray-600 mt-2">
            {mode === "create"
              ? "Create a new accommodation listing"
              : "Update accommodation details"}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>Basic Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Name *</Label>
              <Input
                id="name"
                {...register("name", { required: "Name is required" })}
                placeholder="e.g., Luxury Safari Suite"
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            {/* Type */}
            <div className="space-y-2">
              <Label htmlFor="type">Type *</Label>
              <Controller
                name="type"
                control={control}
                rules={{ required: "Type is required" }}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select accommodation type" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(ACCOMMODATION_TYPE_LABELS).map(
                        ([key, label]) => (
                          <SelectItem key={key} value={key}>
                            {label}
                          </SelectItem>
                        )
                      )}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.type && (
                <p className="text-sm text-red-600">{errors.type.message}</p>
              )}
            </div>

            {/* Price Range */}
            <div className="space-y-2">
              <Label htmlFor="price_range">Price Range *</Label>
              <Input
                id="price_range"
                {...register("price_range", {
                  required: "Price range is required",
                })}
                placeholder="e.g., $180–$380"
              />
              {errors.price_range && (
                <p className="text-sm text-red-600">
                  {errors.price_range.message}
                </p>
              )}
            </div>

            {/* Capacity */}
            <div className="space-y-2">
              <Label htmlFor="capacity">Capacity (guests) *</Label>
              <Input
                id="capacity"
                type="number"
                min="1"
                {...register("capacity", {
                  required: "Capacity is required",
                  min: { value: 1, message: "Capacity must be at least 1" },
                })}
                placeholder="e.g., 4"
              />
              {errors.capacity && (
                <p className="text-sm text-red-600">
                  {errors.capacity.message}
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle>Description & Features</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                {...register("description", {
                  required: "Description is required",
                })}
                placeholder="Describe the accommodation..."
                rows={4}
              />
              {errors.description && (
                <p className="text-sm text-red-600">
                  {errors.description.message}
                </p>
              )}
            </div>

            {/* Special Features */}
            <div className="space-y-2">
              <Label htmlFor="special_features">Special Features</Label>
              <Textarea
                id="special_features"
                {...register("special_features")}
                placeholder="e.g., Ideal for honeymooners, private butler service available..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Amenities */}
        <Card>
          <CardHeader>
            <CardTitle>Amenities</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Select Amenities</Label>
              <Popover open={amenitiesOpen} onOpenChange={setAmenitiesOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={amenitiesOpen}
                    className="w-full justify-between"
                  >
                    {watchedAmenities?.length > 0
                      ? `${watchedAmenities.length} amenities selected`
                      : "Select amenities..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Search amenities..." />
                    <CommandEmpty>No amenity found.</CommandEmpty>
                    <CommandGroup className="max-h-64 overflow-auto">
                      {ACCOMMODATION_AMENITIES.map((amenity) => (
                        <CommandItem
                          key={amenity}
                          onSelect={() => handleAmenityToggle(amenity)}
                        >
                          <Check
                            className={`mr-2 h-4 w-4 ${
                              watchedAmenities?.includes(amenity)
                                ? "opacity-100"
                                : "opacity-0"
                            }`}
                          />
                          {amenity}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Selected Amenities */}
            {watchedAmenities && watchedAmenities.length > 0 && (
              <div className="space-y-2">
                <Label>Selected Amenities</Label>
                <div className="flex flex-wrap gap-2">
                  {watchedAmenities.map((amenity) => (
                    <Badge
                      key={amenity}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {amenity}
                      <X
                        className="h-3 w-3 cursor-pointer"
                        onClick={() => handleAmenityToggle(amenity)}
                      />
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Images */}
        <Card>
          <CardHeader>
            <CardTitle>Images</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Image Upload */}
            <div className="space-y-2">
              <Label htmlFor="images">Upload Images</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Upload className="mx-auto h-12 w-12 text-gray-400" />
                <div className="mt-4">
                  <Label htmlFor="image-upload" className="cursor-pointer">
                    <span className="mt-2 block text-sm font-medium text-gray-900">
                      Click to upload images
                    </span>
                    <span className="mt-1 block text-sm text-gray-500">
                      PNG, JPG, GIF up to 10MB each
                    </span>
                  </Label>
                  <Input
                    id="image-upload"
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                  />
                </div>
              </div>
            </div>

            {/* Existing Images */}
            {watchedImages && watchedImages.length > 0 && (
              <div className="space-y-2">
                <Label>Current Images</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {watchedImages.map((imageUrl, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={imageUrl}
                        alt={`Accommodation image ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleImageRemove(index, true)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* New Images Preview */}
            {watch("images") && watch("images").length > 0 && (
              <div className="space-y-2">
                <Label>New Images</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {watch("images").map((file, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={URL.createObjectURL(file)}
                        alt={`New image ${index + 1}`}
                        className="w-full h-24 object-cover rounded-lg"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleImageRemove(index, false)}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Status & Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Status & Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Status */}
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <Select value={field.value} onValueChange={field.onChange}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="unpublished">Unpublished</SelectItem>
                    </SelectContent>
                  </Select>
                )}
              />
            </div>

            {/* Featured */}
            <div className="flex items-center space-x-2">
              <Controller
                name="featured"
                control={control}
                render={({ field }) => (
                  <Checkbox
                    id="featured"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                )}
              />
              <Label htmlFor="featured">Featured accommodation</Label>
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex items-center justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => navigate("/admin/accommodations")}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={saving || imageUploading}
            className="bg-amber-600 hover:bg-amber-700"
          >
            {saving || imageUploading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                {imageUploading ? "Uploading..." : "Saving..."}
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                {mode === "create"
                  ? "Create Accommodation"
                  : "Update Accommodation"}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
