import React, { useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/use-auth";
import { isStaffOrAdmin, isSessionValid } from "@/lib/auth";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAdmin?: boolean;
}

export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAdmin = false,
}) => {
  const { user, profile, loading, signOut } = useAuth();
  const location = useLocation();
  const [sessionChecked, setSessionChecked] = useState(false);
  const [sessionExpired, setSessionExpired] = useState(false);

  // Check session validity when component mounts or user changes
  useEffect(() => {
    const checkSession = async () => {
      if (user && !loading) {
        const valid = await isSessionValid();
        if (!valid) {
          console.log("Session is invalid, signing out user");
          setSessionExpired(true);
          await signOut();
        }
        setSessionChecked(true);
      } else if (!user && !loading) {
        setSessionChecked(true);
      }
    };

    checkSession();
  }, [user, loading, signOut]);

  // Debug logging for development
  if (process.env.NODE_ENV === "development") {
    console.log("ProtectedRoute state:", {
      hasUser: !!user,
      hasProfile: !!profile,
      profileRole: profile?.role,
      loading,
      sessionChecked,
      sessionExpired,
    });
  }

  if (loading || !sessionChecked) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-amber-600"></div>
      </div>
    );
  }

  if (!user || sessionExpired) {
    return <Navigate to="/admin/login" state={{ from: location }} replace />;
  }

  if (!profile || !isStaffOrAdmin(profile)) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Access Denied
          </h1>
          <p className="text-gray-600 mb-4">
            You don't have permission to access this area.
          </p>
          <button
            onClick={() => signOut()}
            className="px-4 py-2 bg-amber-600 text-white rounded hover:bg-amber-700 transition-colors"
          >
            Sign Out
          </button>
          {process.env.NODE_ENV === "development" && (
            <div className="mt-4 text-sm text-gray-500">
              Debug: User ID: {user?.id}, Profile:{" "}
              {profile ? JSON.stringify(profile) : "null"}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (requireAdmin && profile.role !== "admin") {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Admin Access Required
          </h1>
          <p className="text-gray-600">
            This area requires administrator privileges.
          </p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};
